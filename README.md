# ROS2 PointCloud2 订阅者示例

这个项目包含了订阅ROS2 PointCloud2消息的示例代码，专门用于监听 `alg/fusion/occ_points` topic。

## 文件说明

- `pointcloud_subscriber.py` - Python版本的订阅者
- `pointcloud_subscriber.cpp` - C++版本的订阅者  
- `CMakeLists.txt` - CMake构建配置
- `package.xml` - ROS2包配置文件

## 功能特性

两个版本的订阅者都提供以下功能：

1. **订阅PointCloud2消息** - 监听 `alg/fusion/occ_points` topic
2. **消息信息显示** - 显示时间戳、坐标系、点云尺寸等基本信息
3. **点云数据解析** - 将PointCloud2转换为可处理的点坐标数据
4. **统计信息** - 显示点云的坐标范围、中心点等统计信息
5. **示例点显示** - 打印前几个点的具体坐标

## 依赖要求

### Python版本依赖
```bash
sudo apt install ros-humble-sensor-msgs-py
pip3 install numpy
```

### C++版本依赖
```bash
sudo apt install ros-humble-pcl-conversions
sudo apt install libpcl-dev
```

## 编译和运行

### 1. 编译包
```bash
# 在工作空间根目录下
colcon build --packages-select pointcloud_subscriber
source install/setup.bash
```

### 2. 运行Python版本
```bash
ros2 run pointcloud_subscriber pointcloud_subscriber.py
```

### 3. 运行C++版本
```bash
ros2 run pointcloud_subscriber pointcloud_subscriber
```

## 输出示例

当接收到PointCloud2消息时，程序会输出类似以下信息：

```
[INFO] [pointcloud_subscriber]: 收到PointCloud2消息:
[INFO] [pointcloud_subscriber]:   - 时间戳: 1234567890.123456789
[INFO] [pointcloud_subscriber]:   - 坐标系: base_link
[INFO] [pointcloud_subscriber]:   - 点云尺寸: 1024 x 1
[INFO] [pointcloud_subscriber]:   - 点数量: 1024
[INFO] [pointcloud_subscriber]:   - 数据大小: 12288 bytes
[INFO] [pointcloud_subscriber]:   - 成功解析 1024 个点
[INFO] [pointcloud_subscriber]:   - 前5个点的坐标:
[INFO] [pointcloud_subscriber]:     点1: x=1.234, y=2.345, z=3.456
[INFO] [pointcloud_subscriber]:     点2: x=1.235, y=2.346, z=3.457
[INFO] [pointcloud_subscriber]:     ...
[INFO] [pointcloud_subscriber]:   - X范围: [-10.000, 10.000]
[INFO] [pointcloud_subscriber]:   - Y范围: [-5.000, 5.000]
[INFO] [pointcloud_subscriber]:   - Z范围: [0.000, 3.000]
```

## 测试

确保有其他节点在发布 `alg/fusion/occ_points` topic的PointCloud2消息，然后运行订阅者即可看到输出。

可以使用以下命令检查topic是否存在：
```bash
ros2 topic list | grep alg/fusion/occ_points
ros2 topic info alg/fusion/occ_points
```

## 自定义修改

如果需要订阅不同的topic，只需修改代码中的topic名称：
- Python版本：修改 `pointcloud_subscriber.py` 中的 `'alg/fusion/occ_points'`
- C++版本：修改 `pointcloud_subscriber.cpp` 中的 `"alg/fusion/occ_points"`
