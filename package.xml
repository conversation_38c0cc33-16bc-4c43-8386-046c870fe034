<?xml version="1.0"?>
<?xml-model href="http://download.ros.org/schema/package_format3.xsd" schematypens="http://www.w3.org/2001/XMLSchema"?>
<package format="3">
  <name>pointcloud_subscriber</name>
  <version>0.0.0</version>
  <description>ROS2 PointCloud2 subscriber example for alg/fusion/occ_points topic</description>
  <maintainer email="<EMAIL>">user</maintainer>
  <license>Apache-2.0</license>

  <buildtool_depend>ament_cmake</buildtool_depend>

  <depend>rclcpp</depend>
  <depend>sensor_msgs</depend>
  <depend>pcl_conversions</depend>
  <depend>libpcl-all-dev</depend>

  <exec_depend>rclpy</exec_depend>
  <exec_depend>sensor_msgs_py</exec_depend>

  <test_depend>ament_lint_auto</test_depend>
  <test_depend>ament_lint_common</test_depend>

  <export>
    <build_type>ament_cmake</build_type>
  </export>
</package>
