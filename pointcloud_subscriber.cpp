#include <nlibcpp/nlibcpp.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <cstring>
#include <algorithm>
#include <vector>
#include <cmath>

// 简单的点结构
struct Point3D {
    float x, y, z;
};

// 解析PointCloud2数据的函数
void parse_pointcloud2(const sensor_msgs::msg::PointCloud2& cloud_msg,
                      std::vector<Point3D>& points)
{
    points.clear();

    // 查找x, y, z字段的偏移量
    int x_offset = -1, y_offset = -1, z_offset = -1;

    for (size_t i = 0; i < cloud_msg.fields.size(); ++i) {
        if (cloud_msg.fields[i].name == "x") {
            x_offset = cloud_msg.fields[i].offset;
        } else if (cloud_msg.fields[i].name == "y") {
            y_offset = cloud_msg.fields[i].offset;
        } else if (cloud_msg.fields[i].name == "z") {
            z_offset = cloud_msg.fields[i].offset;
        }
    }

    // 如果找不到xyz字段，返回
    if (x_offset == -1 || y_offset == -1 || z_offset == -1) {
        return;
    }

    // 解析每个点
    size_t point_step = cloud_msg.point_step;
    size_t num_points = cloud_msg.width * cloud_msg.height;

    for (size_t i = 0; i < num_points; ++i) {
        size_t point_offset = i * point_step;

        // 提取x, y, z坐标
        float x, y, z;
        std::memcpy(&x, &cloud_msg.data[point_offset + x_offset], sizeof(float));
        std::memcpy(&y, &cloud_msg.data[point_offset + y_offset], sizeof(float));
        std::memcpy(&z, &cloud_msg.data[point_offset + z_offset], sizeof(float));

        // 检查是否为有效点（非NaN）
        if (!std::isnan(x) && !std::isnan(y) && !std::isnan(z)) {
            points.push_back({x, y, z});
        }
    }
}

class PointCloudSubscriber : public nlibcpp::Node
{
public:
    PointCloudSubscriber() : Node("pointcloud_subscriber")
    {
        // 创建订阅者，订阅 alg/fusion/occ_points topic
        subscription_ = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            "alg/fusion/occ_points",
            10,
            std::bind(&PointCloudSubscriber::pointcloud_callback, this, std::placeholders::_1)
        );
        
        NLIBCPP_INFO(this->get_logger(), "PointCloud2订阅者已启动，正在监听topic: alg/fusion/occ_points");
    }

private:
    void pointcloud_callback(const sensor_msgs::msg::PointCloud2::SharedPtr msg)
    {
        // 解析点云数据
        std::vector<Point3D> points;
        parse_pointcloud2(*msg, points);

        // 打印点云数量
        NLIBCPP_INFO(this->get_logger(), "收到点云数据，共 %zu 个点", points.size());
    }

    nlibcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr subscription_;
};

int main(int argc, char** argv)
{
    nlibcpp::init(argc, argv);

    // 创建节点
    auto node = std::make_shared<PointCloudSubscriber>();

    try {
        // 运行节点
        nlibcpp::spin(node);
    } catch (const std::exception& e) {
        NLIBCPP_ERROR(node->get_logger(), "节点运行出错: %s", e.what());
    }

    // 清理
    nlibcpp::shutdown();
    return 0;
}
