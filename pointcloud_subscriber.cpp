#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <pcl_conversions/pcl_conversions.h>
#include <pcl/point_cloud.h>
#include <pcl/point_types.h>
#include <pcl/common/common.h>

class PointCloudSubscriber : public rclcpp::Node
{
public:
    PointCloudSubscriber() : Node("pointcloud_subscriber")
    {
        // 创建订阅者，订阅 alg/fusion/occ_points topic
        subscription_ = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            "alg/fusion/occ_points",
            10,
            std::bind(&PointCloudSubscriber::pointcloud_callback, this, std::placeholders::_1)
        );
        
        RCLCPP_INFO(this->get_logger(), "PointCloud2订阅者已启动，正在监听topic: alg/fusion/occ_points");
    }

private:
    void pointcloud_callback(const sensor_msgs::msg::PointCloud2::SharedPtr msg)
    {
        RCLCPP_INFO(this->get_logger(), "收到PointCloud2消息:");
        RCLCPP_INFO(this->get_logger(), "  - 时间戳: %d.%d", 
                    msg->header.stamp.sec, msg->header.stamp.nanosec);
        RCLCPP_INFO(this->get_logger(), "  - 坐标系: %s", msg->header.frame_id.c_str());
        RCLCPP_INFO(this->get_logger(), "  - 点云尺寸: %d x %d", msg->width, msg->height);
        RCLCPP_INFO(this->get_logger(), "  - 点数量: %d", msg->width * msg->height);
        RCLCPP_INFO(this->get_logger(), "  - 数据大小: %zu bytes", msg->data.size());

        try {
            // 将ROS2 PointCloud2转换为PCL点云
            pcl::PointCloud<pcl::PointXYZ>::Ptr cloud(new pcl::PointCloud<pcl::PointXYZ>);
            pcl::fromROSMsg(*msg, *cloud);

            if (!cloud->empty()) {
                RCLCPP_INFO(this->get_logger(), "  - 成功解析 %zu 个点", cloud->size());

                // 打印前几个点的坐标信息
                size_t num_points_to_show = std::min(static_cast<size_t>(5), cloud->size());
                RCLCPP_INFO(this->get_logger(), "  - 前%zu个点的坐标:", num_points_to_show);
                
                for (size_t i = 0; i < num_points_to_show; ++i) {
                    const auto& point = cloud->points[i];
                    RCLCPP_INFO(this->get_logger(), "    点%zu: x=%.3f, y=%.3f, z=%.3f", 
                               i+1, point.x, point.y, point.z);
                }

                // 计算点云的边界框
                pcl::PointXYZ min_pt, max_pt;
                pcl::getMinMax3D(*cloud, min_pt, max_pt);
                
                RCLCPP_INFO(this->get_logger(), "  - X范围: [%.3f, %.3f]", min_pt.x, max_pt.x);
                RCLCPP_INFO(this->get_logger(), "  - Y范围: [%.3f, %.3f]", min_pt.y, max_pt.y);
                RCLCPP_INFO(this->get_logger(), "  - Z范围: [%.3f, %.3f]", min_pt.z, max_pt.z);

                // 计算点云中心点
                Eigen::Vector4f centroid;
                pcl::compute3DCentroid(*cloud, centroid);
                RCLCPP_INFO(this->get_logger(), "  - 中心点: x=%.3f, y=%.3f, z=%.3f", 
                           centroid[0], centroid[1], centroid[2]);
            } else {
                RCLCPP_WARN(this->get_logger(), "点云数据为空");
            }
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "解析点云数据时出错: %s", e.what());
        }

        RCLCPP_INFO(this->get_logger(), "--------------------------------------------------");
    }

    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr subscription_;
};

int main(int argc, char** argv)
{
    // 初始化ROS2
    rclcpp::init(argc, argv);

    // 创建节点
    auto node = std::make_shared<PointCloudSubscriber>();

    try {
        // 运行节点
        rclcpp::spin(node);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(node->get_logger(), "节点运行出错: %s", e.what());
    }

    // 清理
    rclcpp::shutdown();
    return 0;
}
