#include <rclcpp/rclcpp.hpp>
#include <sensor_msgs/msg/point_cloud2.hpp>
#include <cstring>
#include <algorithm>
#include <vector>
#include <cmath>

// 简单的点结构
struct Point3D {
    float x, y, z;
};

// 解析PointCloud2数据的函数
void parse_pointcloud2(const sensor_msgs::msg::PointCloud2& cloud_msg,
                      std::vector<Point3D>& points)
{
    points.clear();

    // 查找x, y, z字段的偏移量
    int x_offset = -1, y_offset = -1, z_offset = -1;

    for (size_t i = 0; i < cloud_msg.fields.size(); ++i) {
        if (cloud_msg.fields[i].name == "x") {
            x_offset = cloud_msg.fields[i].offset;
        } else if (cloud_msg.fields[i].name == "y") {
            y_offset = cloud_msg.fields[i].offset;
        } else if (cloud_msg.fields[i].name == "z") {
            z_offset = cloud_msg.fields[i].offset;
        }
    }

    // 如果找不到xyz字段，返回
    if (x_offset == -1 || y_offset == -1 || z_offset == -1) {
        return;
    }

    // 解析每个点
    size_t point_step = cloud_msg.point_step;
    size_t num_points = cloud_msg.width * cloud_msg.height;

    for (size_t i = 0; i < num_points; ++i) {
        size_t point_offset = i * point_step;

        // 提取x, y, z坐标
        float x, y, z;
        std::memcpy(&x, &cloud_msg.data[point_offset + x_offset], sizeof(float));
        std::memcpy(&y, &cloud_msg.data[point_offset + y_offset], sizeof(float));
        std::memcpy(&z, &cloud_msg.data[point_offset + z_offset], sizeof(float));

        // 检查是否为有效点（非NaN）
        if (!std::isnan(x) && !std::isnan(y) && !std::isnan(z)) {
            points.push_back({x, y, z});
        }
    }
}

class PointCloudSubscriber : public rclcpp::Node
{
public:
    PointCloudSubscriber() : Node("pointcloud_subscriber")
    {
        // 创建订阅者，订阅 alg/fusion/occ_points topic
        subscription_ = this->create_subscription<sensor_msgs::msg::PointCloud2>(
            "alg/fusion/occ_points",
            10,
            std::bind(&PointCloudSubscriber::pointcloud_callback, this, std::placeholders::_1)
        );
        
        RCLCPP_INFO(this->get_logger(), "PointCloud2订阅者已启动，正在监听topic: alg/fusion/occ_points");
    }

private:
    void pointcloud_callback(const sensor_msgs::msg::PointCloud2::SharedPtr msg)
    {
        RCLCPP_INFO(this->get_logger(), "收到PointCloud2消息:");
        RCLCPP_INFO(this->get_logger(), "  - 时间戳: %d.%d",
                    msg->header.stamp.sec, msg->header.stamp.nanosec);
        RCLCPP_INFO(this->get_logger(), "  - 坐标系: %s", msg->header.frame_id.c_str());
        RCLCPP_INFO(this->get_logger(), "  - 点云尺寸: %d x %d", msg->width, msg->height);
        RCLCPP_INFO(this->get_logger(), "  - 点数量: %d", msg->width * msg->height);
        RCLCPP_INFO(this->get_logger(), "  - 数据大小: %zu bytes", msg->data.size());

        try {
            // 解析点云数据
            std::vector<Point3D> points;
            parse_pointcloud2(*msg, points);

            if (!points.empty()) {
                RCLCPP_INFO(this->get_logger(), "  - 成功解析 %zu 个点", points.size());

                // 打印前几个点的坐标信息
                size_t num_points_to_show = std::min(static_cast<size_t>(5), points.size());
                RCLCPP_INFO(this->get_logger(), "  - 前%zu个点的坐标:", num_points_to_show);

                for (size_t i = 0; i < num_points_to_show; ++i) {
                    const auto& point = points[i];
                    RCLCPP_INFO(this->get_logger(), "    点%zu: x=%.3f, y=%.3f, z=%.3f",
                               i+1, point.x, point.y, point.z);
                }

                // 计算点云的边界框
                float min_x = points[0].x, max_x = points[0].x;
                float min_y = points[0].y, max_y = points[0].y;
                float min_z = points[0].z, max_z = points[0].z;

                for (const auto& point : points) {
                    min_x = std::min(min_x, point.x);
                    max_x = std::max(max_x, point.x);
                    min_y = std::min(min_y, point.y);
                    max_y = std::max(max_y, point.y);
                    min_z = std::min(min_z, point.z);
                    max_z = std::max(max_z, point.z);
                }

                RCLCPP_INFO(this->get_logger(), "  - X范围: [%.3f, %.3f]", min_x, max_x);
                RCLCPP_INFO(this->get_logger(), "  - Y范围: [%.3f, %.3f]", min_y, max_y);
                RCLCPP_INFO(this->get_logger(), "  - Z范围: [%.3f, %.3f]", min_z, max_z);

                // 计算点云中心点
                float center_x = 0, center_y = 0, center_z = 0;
                for (const auto& point : points) {
                    center_x += point.x;
                    center_y += point.y;
                    center_z += point.z;
                }
                center_x /= points.size();
                center_y /= points.size();
                center_z /= points.size();

                RCLCPP_INFO(this->get_logger(), "  - 中心点: x=%.3f, y=%.3f, z=%.3f",
                           center_x, center_y, center_z);
            } else {
                RCLCPP_WARN(this->get_logger(), "点云数据为空");
            }
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "解析点云数据时出错: %s", e.what());
        }

        RCLCPP_INFO(this->get_logger(), "--------------------------------------------------");
    }

    rclcpp::Subscription<sensor_msgs::msg::PointCloud2>::SharedPtr subscription_;
};

int main(int argc, char** argv)
{
    // 初始化ROS2
    rclcpp::init(argc, argv);

    // 创建节点
    auto node = std::make_shared<PointCloudSubscriber>();

    try {
        // 运行节点
        rclcpp::spin(node);
    } catch (const std::exception& e) {
        RCLCPP_ERROR(node->get_logger(), "节点运行出错: %s", e.what());
    }

    // 清理
    rclcpp::shutdown();
    return 0;
}
