#!/usr/bin/env python3

import rclpy
from rclpy.node import Node
from sensor_msgs.msg import PointCloud2
import sensor_msgs_py.point_cloud2 as pc2
import numpy as np


class PointCloudSubscriber(Node):
    def __init__(self):
        super().__init__('pointcloud_subscriber')
        
        # 创建订阅者，订阅 alg/fusion/occ_points topic
        self.subscription = self.create_subscription(
            PointCloud2,
            'alg/fusion/occ_points',
            self.pointcloud_callback,
            10  # QoS队列大小
        )
        
        self.get_logger().info('PointCloud2订阅者已启动，正在监听topic: alg/fusion/occ_points')
        
    def pointcloud_callback(self, msg):
        """
        PointCloud2消息回调函数
        """
        self.get_logger().info(f'收到PointCloud2消息:')
        self.get_logger().info(f'  - 时间戳: {msg.header.stamp.sec}.{msg.header.stamp.nanosec}')
        self.get_logger().info(f'  - 坐标系: {msg.header.frame_id}')
        self.get_logger().info(f'  - 点云尺寸: {msg.width} x {msg.height}')
        self.get_logger().info(f'  - 点数量: {msg.width * msg.height}')
        self.get_logger().info(f'  - 数据大小: {len(msg.data)} bytes')
        
        # 解析点云数据
        try:
            # 将PointCloud2转换为numpy数组
            points = self.pointcloud2_to_array(msg)
            
            if points is not None and len(points) > 0:
                self.get_logger().info(f'  - 成功解析 {len(points)} 个点')
                
                # 打印前几个点的坐标信息
                num_points_to_show = min(5, len(points))
                self.get_logger().info(f'  - 前{num_points_to_show}个点的坐标:')
                for i in range(num_points_to_show):
                    x, y, z = points[i][:3]  # 取前3个坐标
                    self.get_logger().info(f'    点{i+1}: x={x:.3f}, y={y:.3f}, z={z:.3f}')
                
                # 统计信息
                x_coords = points[:, 0]
                y_coords = points[:, 1] 
                z_coords = points[:, 2]
                
                self.get_logger().info(f'  - X范围: [{x_coords.min():.3f}, {x_coords.max():.3f}]')
                self.get_logger().info(f'  - Y范围: [{y_coords.min():.3f}, {y_coords.max():.3f}]')
                self.get_logger().info(f'  - Z范围: [{z_coords.min():.3f}, {z_coords.max():.3f}]')
            else:
                self.get_logger().warn('点云数据为空或解析失败')
                
        except Exception as e:
            self.get_logger().error(f'解析点云数据时出错: {str(e)}')
        
        self.get_logger().info('-' * 50)
    
    def pointcloud2_to_array(self, cloud_msg):
        """
        将PointCloud2消息转换为numpy数组
        """
        try:
            # 使用sensor_msgs_py库解析点云
            points_list = []
            
            # 遍历点云中的所有点
            for point in pc2.read_points(cloud_msg, skip_nans=True):
                points_list.append(point)
            
            if points_list:
                # 转换为numpy数组
                points_array = np.array(points_list)
                return points_array
            else:
                return None
                
        except Exception as e:
            self.get_logger().error(f'转换点云数据时出错: {str(e)}')
            return None


def main(args=None):
    # 初始化ROS2
    rclpy.init(args=args)
    
    # 创建节点
    pointcloud_subscriber = PointCloudSubscriber()
    
    try:
        # 运行节点
        rclpy.spin(pointcloud_subscriber)
    except KeyboardInterrupt:
        pass
    finally:
        # 清理
        pointcloud_subscriber.destroy_node()
        rclpy.shutdown()


if __name__ == '__main__':
    main()
