cmake_minimum_required(VERSION 3.8)
project(pointcloud_subscriber)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()
set(CMAKE_CXX_STANDARD 14)

# 查找依赖包
# find_package(ament_cmake REQUIRED)
find_package(nlibcpp REQUIRED)
find_package(sensor_msgs REQUIRED)

# 创建可执行文件
add_executable(pointcloud_subscriber pointcloud_subscriber.cpp)

# 链接依赖库
ament_target_dependencies(pointcloud_subscriber
  nlibcpp
  sensor_msgs
)

# 安装可执行文件
install(TARGETS
  pointcloud_subscriber
  DESTINATION lib/${PROJECT_NAME}
)

ament_export_dependencies(${DEPENDENCES})

ament_export_targets (export_${PROJECT_NAME})

ament_package()
